package comp

import (
	"encoding/json"
	"fmt"
	"lxjxmis-go/api"
	"lxjxmis-go/api/tiku"
	tikuCli "lxjxmis-go/api/tiku"
	"lxjxmis-go/components/components_const"
	"lxjxmis-go/components/errcode"
	"lxjxmis-go/components/structs/data"
	"lxjxmis-go/components/structs/form"
	schoolDao "lxjxmis-go/dao/school"
	"lxjxmis-go/helpers"
	"lxjxmis-go/models/school"
	"sort"
	"strconv"
	"time"

	"gorm.io/gorm"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func (s CompService) UpsertCompRound(ctx *gin.Context, param form.UpsertCompRoundRequest) (compRoundId int64, err error) {
	compInfo, err := school.NewCompModel(ctx).GetInfoByCompId(param.CompId)
	if err != nil {
		zlog.Warnf(ctx, "[Detai l] school.GetInfoByCompId failed: err=[%v]", err)
		return
	}
	err = checkCompRound(ctx, compInfo, &param)
	if err != nil {
		return
	}
	defer func() {
		cacheKey := fmt.Sprintf(components_const.CompRoundList, compInfo.CompID)
		_, _ = helpers.RedisClient.Del(ctx, cacheKey)
	}()
	compRoundId, err = UpsertCompRoundProcess(ctx, compInfo, param)
	if err != nil {
		return
	}
	return
}

func (s CompService) DelCompRound(ctx *gin.Context, param form.DelCompRoundRequest) (err error) {

	compRoundModel := school.NewCompRoundModel(ctx)
	compRoundInfo, err := compRoundModel.GetInfoByCompRoundId(param.CompRoundId)
	if err != nil {
		zlog.Warnf(ctx, "[UpsertCompRound] GetInfoByCompRoundId failed: err=[%v],param = %v", err, param)
		return
	}
	if compRoundInfo.CompRoundID == 0 {
		return errcode.SystemError("不存在")
	}

	_, err = compRoundModel.DeleteByCompRoundIds(nil, []int{param.CompRoundId})
	if err != nil {
		zlog.Warnf(ctx, "[UpsertCompRound] DeleteByCompRoundIds failed: err=[%v],param = %v", err, param)
		return err
	}
	compInfo, err := school.NewCompModel(ctx).GetInfoByCompId(param.CompId)
	if err != nil {
		return err
	}
	if time.Now().Unix() >= int64(compInfo.StartTime) {
		return errcode.SystemError("已开始比赛，不能删除")
	}

	compEventList := data.CompEventList{}
	_ = json.Unmarshal([]byte(compInfo.CompEventList), &compEventList)

	compEventFrom := components_const.CompEventFormTypeAnswer
	for _, v := range compEventList {
		if v.CompEventType == compRoundInfo.CompEventType {
			compEventFrom = v.CompEventFrom
		}
	}

	_, err = CompRoundReorder(ctx, nil, param.CompId, compRoundInfo.CompEventType, compRoundInfo.GradeID, compEventFrom)
	if err != nil {
		zlog.Warnf(ctx, "[UpsertCompRound] GetListByCompEventTypeGradeId failed: err=[%v],param = %v", err, param)
		return
	}
	return

}

func (s CompService) GetRoundNumList(ctx *gin.Context, param form.GetRoundNumListRequest) (res []map[string]interface{}, err error) {
	compRoundList, err := school.NewCompRoundModel(s.ctx).
		GetListBycompIdEventType(param.CompID, param.CompEventType)
	if err != nil {
		zlog.Warnf(ctx, "[GetRoundNumList] GetListBycompIdEventType failed: err=[%v],param = %v", err, param)
		return
	}
	compRoundGradeMap := map[int][]int{}
	for _, v := range compRoundList {
		if _, ok := compRoundGradeMap[v.RoundNum]; !ok {
			compRoundGradeMap[v.RoundNum] = make([]int, 0)
		}
		compRoundGradeMap[v.RoundNum] = append(compRoundGradeMap[v.RoundNum], v.CompRoundID)
	}
	res = []map[string]interface{}{}
	for k, v := range compRoundGradeMap {
		res = append(res, map[string]interface{}{
			"roundNum":     k,
			"compRoundIds": v,
		})
	}

	sort.Slice(res, func(i, j int) bool {
		return res[i]["roundNum"].(int) < res[j]["roundNum"].(int)
	})
	return
}

//func (s CompService) UpsertAllCompRound(ctx *gin.Context, compId int, paramList []form.UpsertCompRoundRequest) (err error) {
//	compInfo, err := school.NewCompModel(ctx).GetInfoByCompId(compId)
//	if err != nil {
//		zlog.Warnf(ctx, "[UpsertAllCompRound] school.GetInfoByCompId failed: err=[%v]", err)
//		return err
//	}
//	defer func() {
//		cacheKey := fmt.Sprintf(components_const.CompRoundList, compInfo.CompID)
//		_, _ = helpers.RedisClient.Del(ctx, cacheKey)
//	}()
//	for k := range paramList {
//		err := checkCompRound(ctx, compInfo, &paramList[k])
//		if err != nil {
//			return err
//		}
//	}
//	for _, param := range paramList {
//		_, err = UpsertCompRoundProcess(ctx, compInfo, param)
//		if err != nil {
//			return
//		}
//	}
//	return nil
//}

func checkCompRound(ctx *gin.Context, compInfo schoolDao.CompDao, param *form.UpsertCompRoundRequest) (err error) {
	errStr := "【" + components_const.CompEventTypeMap[param.CompEventType] + "赛项-" + components_const.AllGradeStageListMap[param.GradeId] + "年级】"
	if compInfo.ID == 0 {
		err = errcode.CompNotExist
		return
	}

	if param.CompEventType == components_const.CompEventTypeDaPing {
		examConf := data.InteractiveDiathesisExamConf{}
		err = json.Unmarshal([]byte(param.ExamConf), &examConf)
		if err != nil {
			err = errcode.SystemError(errStr + "取卷及得分规则配置错误，请检查")
			return err
		}
		if len(examConf) == 0 {
			err = errcode.SystemError(errStr + "取卷及得分规则配置错误，请检查")
			return err
		}
		param.InteractiveDiathesisExamConfStruct = examConf // 后续使用
		for i, v := range param.InteractiveDiathesisExamConfStruct {
			endClassId := v.ClassIds[len(v.ClassIds)-1]
			groupType := GetGroupTypeByClassId(endClassId)
			if groupType == 0 {
				return errcode.SystemError(strconv.FormatUint(endClassId, 10) + "分类 id 不存在")
			}
			param.InteractiveDiathesisExamConfStruct[i].GroupType = groupType
		}
		examIdMap := make(map[uint64]bool)
		for _, v := range examConf {
			if len(v.ClassIds) == 0 {
				err = errcode.SystemError(errStr + "分类不可为空")
				return err
			}
			if v.ExamId == 0 {
				err = errcode.SystemError(errStr + "试卷id不可为空")
				return err
			}
			if _, ok := examIdMap[v.ExamId]; ok {
				err = errcode.SystemError(errStr + strconv.FormatUint(v.ExamId, 10) + "试卷id不可重复")
				return err
			}
			//if v.TimeLimit == 0 {
			//	err = errcode.SystemError(errStr + "单题时长不可为空")
			//	return err
			//}
			examIdMap[v.ExamId] = true
		}

		return
	}
	if param.CompEventType != components_const.CompDpEventTypeHanZiWrite && param.CompEventType != components_const.CompEventTypeBatchAiZw {
		// 非大屏比赛
		if param.StartTime < compInfo.StartTime {
			err = errcode.SystemError(errStr + "赛项开始时间不能早于整体比赛开始时间")
			return
		}
		if param.EndTime > compInfo.EndTime {
			err = errcode.SystemError(errStr + "赛项结束时间不能晚于整体比赛结束时间")
			return
		}
	}

	switch param.CompEventType {
	case components_const.CompEventTypeHuaRongRoad:
		{
			// 华容道
			examConf := data.HuaRongRoadExamConf{}
			err = json.Unmarshal([]byte(param.ExamConf), &examConf)
			if err != nil {
				err = errcode.SystemError(errStr + "取卷及得分规则配置错误，请检查")
				return err
			}
			if len(examConf) == 0 {
				err = errcode.SystemError(errStr + "取卷及得分规则配置错误，请检查")
				return err
			}
			param.HuaRongRoadExamConfStruct = examConf // 后续使用
			for _, v := range examConf {
				if len(v.ClassIds) == 0 {
					err = errcode.SystemError(errStr + "取卷及得分规则配置错误，请检查")
					return err
				}
				if len(v.ClassConf) == 0 || len(v.ClassConf) != 2 {
					err = errcode.SystemError(errStr + "最佳完成步数，输入有误，请检查")
					return err
				}

				if v.ClassConf[0] <= 0 || v.ClassConf[0] > 1000 {
					err = errcode.SystemError(errStr + "最佳完成步数，仅可输入数字1~1000")
					return err
				}
				if v.ClassConf[1] <= 0 || v.ClassConf[1] > 1000 {
					err = errcode.SystemError(errStr + "最佳完成步数，仅可输入数字1~1000")
					return err
				}
				if v.ClassConf[1] < v.ClassConf[0] {
					err = errcode.SystemError(errStr + "最佳完成步数，最小步数需小于等于最大步数")
					return err
				}

				if v.TiConf.Score <= 0 || v.TiConf.Score > 200 {
					err = errcode.SystemError(errStr + "每道题分数仅可输入数字1~100")
					return err
				}

				if v.TiConf.Total <= 0 || v.TiConf.Total > 200 {
					err = errcode.SystemError(errStr + "本次固定抽题数仅可输入数字1~20")
					return err
				}

				if v.TiConf.Num <= 0 || v.TiConf.Num > 200 {
					err = errcode.SystemError(errStr + "随机试卷库仅可输入数字1~200")
					return err
				}
			}
		}
	case components_const.CompEventTypeBatchAiZw:
		fallthrough
	case components_const.CompEventTypeAiEssay:
		{
			aiScoringRuleList := data.AiScoringRuleList{}
			err := json.Unmarshal([]byte(param.ScoringRule), &aiScoringRuleList)
			if err != nil {
				err = errcode.SystemError(errStr + "打分规则配置错误，请检查")
				return err
			}

			weight := 0
			for _, v := range aiScoringRuleList {
				if _, ok := components_const.CompRoundScoringRuleMap[v.ScoringRule]; !ok {
					err = errcode.SystemError(errStr + "打分规则配置不存在，请检查")
					return err
				}
				weight += v.Weight
			}
			if weight != 100 {
				err = errcode.SystemError(errStr + "权重配置不等于100%")
				return err
			}
		}
	case components_const.CompEventTypeChineseDiathesis:
		fallthrough
	case components_const.CompEventTypeMathDiathesis:
		fallthrough
	case components_const.CompEventTypeEnglishDiathesis:
		{
			param.ExamType = components_const.CompExamTypeFixed
			examId, err := strconv.Atoi(param.ExamConf)
			if err != nil || examId == 0 {
				err = errcode.SystemError(errStr + "试卷 Id 为空")
				return err
			}
			endClassId := components_const.DiathesisAndTestClassIdMap[param.CompEventType]
			groupType := GetGroupTypeByClassId(endClassId)
			param.InteractiveDiathesisExamConfStruct = []data.InteractiveDiathesisExamConfValue{
				{
					ClassIds:  []uint64{endClassId},
					ExamId:    uint64(examId),
					GroupType: groupType,
				},
			}
		}
	case components_const.CompEventTypeChineseTest:
		fallthrough
	case components_const.CompEventTypeMathTest:
		fallthrough
	case components_const.CompEventTypeEnglishTest:
		fallthrough
	case components_const.CompEventTypeMoralityTest:
		fallthrough
	case components_const.CompEventTypeScienceTest:
		fallthrough
	case components_const.CompEventTypeItTest:
		fallthrough
	case components_const.CompEventTypeLabourTest:
		fallthrough
	case components_const.CompEventTypeMusicTest:
		fallthrough
	case components_const.CompEventTypeArtTest:
		fallthrough
	case components_const.CompEventTypeSportsTest:
		fallthrough
	case components_const.CompEventTypeMentalHealthTest:
		fallthrough
	case components_const.CompEventTypeCommonTest:
		{
			examConf := data.TestExamConf{}
			err = json.Unmarshal([]byte(param.ExamConf), &examConf)
			if err != nil {
				err = errcode.SystemError(errStr + "取卷及得分规则配置错误，请检查")
				return err
			}
			if len(examConf.SelectTiConf) == 0 {
				err = errcode.SystemError(errStr + "取卷及得分规则配置错误，请检查")
				return err
			}
			examConf.ClassID = components_const.DiathesisAndTestClassIdMap[param.CompEventType]
			examConf.GroupType = GetGroupTypeByClassId(examConf.ClassID)
			param.TestExamConfStruct = examConf
		}
	case components_const.CompEventTypeHanZiWrite:
		{
			param.ExamType = components_const.CompExamTypeFixed
			hanZiWriteExamConf := data.HanZiWriteExamConf{}
			err := json.Unmarshal([]byte(param.ExamConf), &hanZiWriteExamConf)
			if err != nil {
				err = errcode.SystemError(errStr + "试卷配置错误，请检查")
				return err
			}
			param.HanZiWriteExamConfStruct = hanZiWriteExamConf
		}
	case components_const.CompDpEventTypeHanZiWrite:
		{
			param.ExamType = components_const.CompExamTypeFixed
			hanZiWriteExamConf := make([]data.HanZiWriteExamConf, 0)
			err := json.Unmarshal([]byte(param.ExamConf), &hanZiWriteExamConf)
			if err != nil {
				err = errcode.SystemError(errStr + "试卷配置错误，请检查")
				return err
			}
			param.DpHanZiWriteExamConfStruct = hanZiWriteExamConf
		}
	default:
		{
			// 通用
			examConf := data.GeneralExamConf{}
			err = json.Unmarshal([]byte(param.ExamConf), &examConf)
			if err != nil {
				err = errcode.SystemError(errStr + "取卷及得分规则配置错误，请检查")
				return err
			}
			if len(examConf) == 0 {
				err = errcode.SystemError(errStr + "取卷及得分规则配置错误，请检查")
				return err
			}
			param.GeneralExamConfStruct = examConf // 后续使用
			for _, v := range examConf {
				if len(v.ClassIds) == 0 {
					err = errcode.SystemError(errStr + "取卷及得分规则配置错误，请检查")
					return err
				}
				if v.TiConf.Score <= 0 || v.TiConf.Score > 200 {
					err = errcode.SystemError(errStr + "每道题分数仅可输入数字1~100")
					return err
				}

				if v.TiConf.Total <= 0 || v.TiConf.Total > 200 {
					err = errcode.SystemError(errStr + "随机试卷库仅可输入数字1~200")
					return err
				}

				if v.TiConf.Num <= 0 || v.TiConf.Num > 20 {
					err = errcode.SystemError(errStr + "本次固定抽题数仅可输入数字1~20")
					return err
				}
			}
		}
	}

	return
}
func UpsertCompRoundProcess(ctx *gin.Context, compInfo schoolDao.CompDao, param form.UpsertCompRoundRequest) (compRoundId int64, err error) {
	if param.RoundNum == 0 {
		param.RoundNum = 1
	}
	compRoundId = int64(param.CompRoundId)
	compRoundModel := school.NewCompRoundModel(ctx)

	defer func() {
		if param.CompRoundId != 0 {
			cacheKey := fmt.Sprintf(components_const.CompRoundExamInfo, compInfo.CompID, param.CompRoundId)
			cacheKey1 := fmt.Sprintf(components_const.CompRoundInfo, param.CompRoundId)
			_, _ = helpers.RedisClient.Del(ctx, cacheKey, cacheKey1)
		}
	}()

	errStr := "【" + components_const.CompEventTypeMap[param.CompEventType] + "赛项" + components_const.AllGradeStageListMap[param.GradeId] + "年级】"
	if param.CompRoundId == 0 {
		if compInfo.LargeScreen == components_const.CompLargeScreenYes || (compInfo.ActType == components_const.ActTestType && compInfo.SupportReport == components_const.TestSupportReportYes) {
			res, err := compRoundModel.GetListByCompEventTypeGradeId(compInfo.CompID, param.CompEventType, param.GradeId)
			if err != nil {
				zlog.Warnf(ctx, "[UpsertCompRound] GetListByCompEventTypeGradeId failed: err=[%v],compRoundId = %d", err, compRoundId)
				return 0, err
			}
			if len(res) > 0 {
				return 0, errcode.SystemError(errStr + "大屏排行启动或者开启测试报告时，仅可配置一轮")
			}
		}

		compRoundId, err = api.SingleId(ctx, api.GalaxyTurnId)
		if err != nil {
			zlog.Warnf(ctx, "[UpsertCompRound] SingleId failed: err=[%v],compRoundId = %d", err, compRoundId)
			return 0, err
		}
	} else {
		_compRoundInfo, err := compRoundModel.GetInfoByCompRoundId(param.CompRoundId)
		if err != nil {
			zlog.Warnf(ctx, "[UpsertCompRound] GetInfoByCompRoundId failed: err=[%v],compRoundId = %d", err, compRoundId)
			return 0, err
		}
		if _compRoundInfo.GradeID != param.GradeId {
			zlog.Warnf(ctx, "[UpsertCompRound] grade err compRoundInfo: err=[%v],gradeId = %v", _compRoundInfo, param.GradeId)
			return 0, errcode.SystemError(errStr + "与当前赛项年级不符")
		}

		if compInfo.LargeScreen == components_const.CompLargeScreenYes || (compInfo.ActType == components_const.ActTestType && compInfo.SupportReport == components_const.TestSupportReportYes) {
			res, err := compRoundModel.GetListByCompEventTypeGradeId(compInfo.CompID, param.CompEventType, param.GradeId)
			if err != nil {
				zlog.Warnf(ctx, "[UpsertCompRound] GetListByCompEventTypeGradeId failed: err=[%v],compRoundId = %d", err, compRoundId)
				return 0, err
			}
			if len(res) > 1 {
				return 0, errcode.SystemError(errStr + "大屏排行启动或者开启测试报告时，仅可配置一轮")
			} else if len(res) == 1 {
				if param.CompRoundId != res[0].CompRoundID {
					if len(res) > 0 {
						return 0, errcode.SystemError(errStr + "大屏排行启动或者开启测试报告时，仅可配置一轮")
					}
				}
			}
		}
	}

	if param.CompRoundId != 0 && int64(compInfo.StartTime) < time.Now().Unix() {
		if compInfo.ActType == components_const.ActInteractiveType {
			// 大屏开始后，不能修改任何信息
			return
		}

		// 比赛已开始，只能更改赛项名
		_, err = compRoundModel.UpdateDataByCompRoundId(nil, param.CompRoundId, schoolDao.CompRoundDao{
			Name: param.Name,
		})
		if err != nil {
			zlog.Warnf(ctx, "[UpsertCompRound] UpdateDataByCompRoundId failed: err=[%v],compRoundId = %d", err, compRoundId)
			return
		}
		return
	}

	if param.CompEventType != components_const.CompEventTypeDaPing && param.CompEventType != components_const.CompDpEventTypeHanZiWrite && param.CompEventType != components_const.CompEventTypeBatchAiZw {
		now := time.Now().Unix()
		if int64(param.StartTime) < now {
			return 0, errcode.SystemError(errStr + "比赛时间不能早于当前")
		}

		if int64(param.StartTime) > int64(param.EndTime) {
			return 0, errcode.SystemError(errStr + "结束时间不能早于开始时间")
		}
	}

	if param.CompEventType == components_const.CompEventTypeDaPing ||
		param.CompEventType == components_const.CompEventTypeChineseDiathesis ||
		param.CompEventType == components_const.CompEventTypeMathDiathesis ||
		param.CompEventType == components_const.CompEventTypeEnglishDiathesis {
		// 增加默认字段
		//param.StartTime = compInfo.StartTime
		//param.EndTime = compInfo.EndTime
		tiUniqMap := make(map[string]bool)
		for _, v := range param.InteractiveDiathesisExamConfStruct {
			tiScoreList, err := getTidListByExamId(ctx, strconv.FormatUint(v.ExamId, 10))
			if err != nil {
				zlog.Warnf(ctx, "获取试卷 id = "+strconv.FormatUint(v.ExamId, 10))
				return 0, errcode.SystemError(errStr + "试卷 id = " + strconv.FormatUint(v.ExamId, 10) + "获取题目失败")
			}
			if len(tiScoreList) == 0 {
				return 0, errcode.SystemError(errStr + "试卷 id = " + strconv.FormatUint(v.ExamId, 10) + "题目数量为空, 没有确认试卷")
			}
			for _, t := range tiScoreList {
				if _, ok := tiUniqMap[t.Tid]; ok {
					return 0, errcode.SystemError(errStr + "试卷 id = " + strconv.FormatUint(v.ExamId, 10) + ",题 id = " + t.Tid + "在其他组有重复题目")
				}
				tiUniqMap[t.Tid] = true
			}

			tiKuClassId := v.ClassIds[len(v.ClassIds)-1]
			examDraftId := fmt.Sprintf("InteractiveDiathesisExamDraft_%d_%d_%d_%d_%d_%d", param.CompId, param.CompEventType, param.GradeId, param.RoundNum, v.ExamId, tiKuClassId)
			err = NewCompService(ctx).setInteractiveExamDraftCacheByDraftID(examDraftId, tiScoreList)
			if err != nil {
				return 0, errcode.SystemError(errStr + "试卷 id = " + strconv.FormatUint(v.ExamId, 10) + "设置草稿失败")
			}
		}
	}

	// 如果是测评活动
	if param.CompEventType == components_const.CompEventTypeChineseTest ||
		param.CompEventType == components_const.CompEventTypeMathTest ||
		param.CompEventType == components_const.CompEventTypeEnglishTest ||
		param.CompEventType == components_const.CompEventTypeMoralityTest ||
		param.CompEventType == components_const.CompEventTypeScienceTest ||
		param.CompEventType == components_const.CompEventTypeItTest ||
		param.CompEventType == components_const.CompEventTypeLabourTest ||
		param.CompEventType == components_const.CompEventTypeMusicTest ||
		param.CompEventType == components_const.CompEventTypeArtTest ||
		param.CompEventType == components_const.CompEventTypeSportsTest ||
		param.CompEventType == components_const.CompEventTypeMentalHealthTest ||
		param.CompEventType == components_const.CompEventTypeCommonTest {

		var (
			examID    = param.TestExamConfStruct.ExamID
			classID   = param.TestExamConfStruct.ClassID
			tiUniqMap = make(map[string]bool)
		)
		tiScoreList, err := getTidListByExamId(ctx, strconv.FormatUint(examID, 10))
		if err != nil {
			zlog.Warnf(ctx, "获取试卷 id = "+strconv.FormatUint(examID, 10))
			return 0, errcode.SystemError(errStr + "试卷 id = " + strconv.FormatUint(examID, 10) + "获取题目失败")
		}
		if len(tiScoreList) == 0 {
			return 0, errcode.SystemError(errStr + "试卷 id = " + strconv.FormatUint(examID, 10) + "题目数量为空, 没有确认试卷")
		}
		for _, t := range tiScoreList {
			if _, ok := tiUniqMap[t.Tid]; ok {
				return 0, errcode.SystemError(errStr + "试卷 id = " + strconv.FormatUint(examID, 10) + ",题 id = " + t.Tid + "在其他组有重复题目")
			}
			tiUniqMap[t.Tid] = true
		}

		examDraftId := fmt.Sprintf("TestExamDraft_%d_%d_%d_%d_%d_%d", param.CompId, param.CompEventType, param.GradeId, param.RoundNum, examID, classID)
		err = NewCompService(ctx).setTestExamDraftCacheByDraftID(examDraftId, tiScoreList)
		if err != nil {
			return 0, errcode.SystemError(errStr + "试卷 id = " + strconv.FormatUint(examID, 10) + "设置草稿失败")
		}
	}

	if param.CompEventType == components_const.CompEventTypeAiEssay || param.CompEventType == components_const.CompEventTypeBatchAiZw {
		// 获取题 ID
		aiCustomMade := data.AiCustomMade{}
		if param.QuestionShow == components_const.CompRoundAiQuestionShowCustomMode {
			err = json.Unmarshal([]byte(param.CustomMade), &aiCustomMade)
			if err != nil {
				zlog.Warnf(ctx, "[UpsertCompRound] json.Unmarshal failed: err=[%v],param = %v", err, param)
				return 0, errcode.SystemError(errStr + "解析自定义参数失败")
			}
		}
		resp, err := tikuCli.QuestionAddEssay(ctx,
			param.ArticleType,
			param.ArticleTheme,
			param.ArticleWordsNum,
			param.Content,
			param.Other,
			param.QuestionShow,
			aiCustomMade,
		)

		if err != nil {
			zlog.Warnf(ctx, "[UpsertCompRound] QuestionAddEssay failed: err=[%v],param = %v", err, param)
			return 0, errcode.SystemError(errStr + "生成题 ID 失败")
		}
		tid := resp.Tid
		if tid == "" {
			zlog.Warnf(ctx, "[UpsertCompRound] QuestionAddEssay failed: err=[%v],param = %v", err, param)
			return 0, errcode.SystemError(errStr + "生成题 ID 为空")
		}
		examDraftId := fmt.Sprintf("AiEssayExamDraft_%d_%d_%d_%d", param.CompId, param.CompEventType, param.GradeId, param.RoundNum)
		err = NewCompService(ctx).setAiEssayExamDraftCacheByDraftID(examDraftId, []string{tid})
		if err != nil {
			return 0, errcode.SystemError(errStr + "设置草稿失败")
		}
	}

	if param.CompEventType == components_const.CompEventTypeHanZiWrite {
		// 获取题目详情
		tids := make([]string, 0, len(param.HanZiWriteExamConfStruct.TiConf))
		tidMap := make(map[string]struct{}, len(param.HanZiWriteExamConfStruct.TiConf))
		if param.CompEventType == components_const.CompDpEventTypeHanZiWrite {
			param.TimeLimit = len(param.HanZiWriteExamConfStruct.TiConf) * param.HanZiWriteExamConfStruct.TimeLimit
		}
		for _, v := range param.HanZiWriteExamConfStruct.TiConf {
			tids = append(tids, v.TId)
			if _, ok := tidMap[v.TId]; ok {
				zlog.Warnf(ctx, "[HanziTiPreview] ti empty failed")
				return 0, errcode.SystemError(v.TId + " 重复，请更改")
			}
		}
		if len(tids) == 0 {
			zlog.Warnf(ctx, "[UpsertCompRound] ti empty failed")
			return 0, errcode.SystemError("题目为空")
		}

		tiListResult, err := tiku.GetTiInfoListByTiIds(ctx, tids)
		if err != nil {
			zlog.Warnf(ctx, "[UpsertCompRound] api.GetTiInfoListByTiIds failed: err=[%v]", err)
			return 0, err
		}

		for _, tid := range tids {
			value, ok := tiListResult[tid]
			if !ok {
				err = errcode.SystemError(tid + "不存在")
				return 0, err
			}
			if value.Category.ID != components_const.TiCategoryTypeHanziTYWrite && value.Category.ID != components_const.TiCategoryTypeHanziMXWrite {
				err = errcode.SystemError(tid + "题类型不符合")
				return 0, err
			}
		}
	}
	if param.CompEventType == components_const.CompDpEventTypeHanZiWrite {
		// 获取题目详情
		tids := make([]string, 0)
		tidMap := make(map[string]struct{})
		param.TimeLimit = 0
		for _, v := range param.DpHanZiWriteExamConfStruct {
			param.TimeLimit += len(v.TiConf) * v.TimeLimit
			for _, vv := range v.TiConf {
				tids = append(tids, vv.TId)
				if _, ok := tidMap[vv.TId]; ok {
					zlog.Warnf(ctx, "[DpHanziTiPreview] ti empty failed")
					return 0, errcode.SystemError(vv.TId + " 重复，请更改")
				}
			}
		}
		if len(tids) == 0 {
			zlog.Warnf(ctx, "[UpsertCompRound] ti empty failed")
			return 0, errcode.SystemError("题目为空")
		}

		tiListResult, err := tiku.GetTiInfoListByTiIds(ctx, tids)
		if err != nil {
			zlog.Warnf(ctx, "[UpsertCompRound] api.GetTiInfoListByTiIds failed: err=[%v]", err)
			return 0, err
		}

		for _, tid := range tids {
			value, ok := tiListResult[tid]
			if !ok {
				err = errcode.SystemError(tid + "不存在")
				return 0, err
			}
			if value.Category.ID != components_const.TiCategoryTypeHanziTYWrite && value.Category.ID != components_const.TiCategoryTypeHanziMXWrite {
				err = errcode.SystemError(tid + "题类型不符合")
				return 0, err
			}
		}
	}

	// 开启事务
	tx := helpers.MysqlClientLxjxSchool.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil || err != nil {
			tx.Rollback()
		}
	}()
	// 写赛项表
	err = UpsertCompRoundRecord(ctx, tx, param, int(compRoundId))
	if err != nil {
		zlog.Warnf(ctx, "[UpsertCompRound] UpsertCompRoundRecord failed: err=[%v],compRoundId = %d", err, compRoundId)
		tx.Rollback()
		return
	}
	// 写试卷表
	err = UpsertCompRoundExamRecord(ctx, tx, param, int(compRoundId))
	if err != nil {
		zlog.Warnf(ctx, "[UpsertCompRound] UpsertCompRoundExamRecord failed: err=[%v],compRoundId = %d", err, compRoundId)
		tx.Rollback()
		return
	}
	// 写题目表
	err = UpsertCompRoundExamTiRecord(ctx, tx, param, int(compRoundId))
	if err != nil {
		zlog.Warnf(ctx, "[UpsertCompRound] UpsertCompRoundExamTiRecord failed: err=[%v],compRoundId = %d", err, compRoundId)
		tx.Rollback()
		return
	}
	//更新报名表comproundid
	if param.CompEventType == components_const.CompEventTypeBatchAiZw {
		//批量更新comproundid
		err = school.NewCompStudentAnswerRegistModel(ctx).UpdateStuRoundId(nil, int64(param.CompId), param.CompEventType, param.GradeId, int(compRoundId))
		if err != nil {
			zlog.Warnf(ctx, "[UpsertCompRound] GetListByCompEventTypeGradeId failed: err=[%v],param = %v", err, param)
			tx.Rollback()
			return
		}
	}
	tx.Commit()
	// 录入花名册
	// err = UpsertCompRoundRosterRecord(ctx, compInfo)
	// if err != nil {
	//	return
	// }
	return
}

func UpsertCompRoundRecord(ctx *gin.Context, tx *gorm.DB, param form.UpsertCompRoundRequest, compRoundId int) (err error) {
	compRoundModel := school.NewCompRoundModel(ctx)
	if param.CompRoundId != 0 {
		// _, err := compRoundModel.DeleteByCompRoundIds([]int{param.CompRoundId})
		// if err != nil {
		//	return err
		// }
		_, err = compRoundModel.UpdateDataByCompRoundId(tx, param.CompRoundId, schoolDao.CompRoundDao{
			Name:      param.Name,
			StartTime: param.StartTime,
			EndTime:   param.EndTime,
			//CompEventType: param.CompEventType,
			//GradeID:       param.GradeId,
			//RoundNum:      param.RoundNum,
			ShowRank:        param.ShowRank,
			CompTimes:       param.CompTimes,
			TimeLimit:       param.TimeLimit * 60,
			ExamType:        param.ExamType,
			ExamConf:        param.ExamConf,
			AnswerSet:       param.AnswerSet,
			ArticleType:     param.ArticleType,
			ArticleTheme:    param.ArticleTheme,
			ArticleWordsNum: param.ArticleWordsNum,
			Content:         param.Content,
			Other:           param.Other,
			QuestionShow:    param.QuestionShow,
			CustomMade:      param.CustomMade,
			ResultShow:      param.ResultShow,
			CommentShow:     param.CommentShow,
			ScoringRule:     param.ScoringRule,
			HzScoringRule:   param.HzScoringRule,
			PublishResult:   components_const.CompPublishResultNo,
		})
		if err != nil {
			return err
		}
		compRoundId = param.CompRoundId
		return
	}

	compInfo, err := school.NewCompModel(ctx).GetInfoByCompId(param.CompId)
	if err != nil {
		return err
	}
	compEventList := data.CompEventList{}
	_ = json.Unmarshal([]byte(compInfo.CompEventList), &compEventList)

	compEventFrom := components_const.CompEventFormTypeAnswer
	for _, v := range compEventList {
		if v.CompEventType == param.CompEventType {
			compEventFrom = v.CompEventFrom
		}
	}
	param.RoundNum, err = CompRoundReorder(ctx, tx, param.CompId, param.CompEventType, param.GradeId, compEventFrom)
	if err != nil {
		zlog.Warnf(ctx, "[UpsertCompRound] GetListByCompEventTypeGradeId failed: err=[%v],param = %v", err, param)
		return
	}

	name := param.Name
	if compEventFrom == components_const.CompEventFormTypePassLevel {
		name = fmt.Sprintf("第%d关", param.RoundNum)
	}
	err = compRoundModel.Add(tx, schoolDao.CompRoundDao{
		CompID:          param.CompId,
		CompRoundID:     compRoundId,
		Name:            name,
		StartTime:       param.StartTime,
		EndTime:         param.EndTime,
		CompEventType:   param.CompEventType,
		GradeID:         param.GradeId,
		RoundNum:        param.RoundNum,
		ShowRank:        param.ShowRank,
		CompTimes:       param.CompTimes,
		TimeLimit:       param.TimeLimit * 60,
		ExamType:        param.ExamType,
		ExamConf:        param.ExamConf,
		AnswerSet:       param.AnswerSet,
		ArticleType:     param.ArticleType,
		ArticleTheme:    param.ArticleTheme,
		ArticleWordsNum: param.ArticleWordsNum,
		Content:         param.Content,
		Other:           param.Other,
		QuestionShow:    param.QuestionShow,
		CustomMade:      param.CustomMade,
		ResultShow:      param.ResultShow,
		CommentShow:     param.CommentShow,
		ScoringRule:     param.ScoringRule,
		HzScoringRule:   param.HzScoringRule,
		PublishResult:   components_const.CompPublishResultNo,
	})
	if err != nil {
		// 回滚
		return
	}
	return
}

func CompRoundReorder(ctx *gin.Context, tx *gorm.DB, compId, compEventType, gradeId, compEventFrom int) (roundNum int, err error) {
	compRoundModel := school.NewCompRoundModel(ctx)

	roundList, err := compRoundModel.GetListByCompEventTypeGradeId(compId, compEventType, gradeId)
	if err != nil {
		zlog.Warnf(ctx, "[CompRoundReorder] GetListByCompEventTypeGradeId failed: err=[%v],param = %v,%v,%v", err, compId, compEventType, gradeId)
		return
	}
	if len(roundList) >= 50 {
		return 0, errcode.SystemError("轮次最大限制添加50轮")
	}
	for k, v := range roundList {
		if v.RoundNum == k+1 {
			continue
		}
		if compEventFrom == components_const.CompEventFormTypePassLevel {
			_, err = compRoundModel.UpdateDataMapByCompRoundId(tx, v.CompRoundID, map[string]interface{}{
				"round_num": k + 1,
				"name":      fmt.Sprintf("第%d关", k+1),
			})
		} else {
			_, err = compRoundModel.UpdateDataMapByCompRoundId(tx, v.CompRoundID, map[string]interface{}{
				"round_num": k + 1,
			})
		}

		if err != nil {
			zlog.Warnf(ctx, "[CompRoundReorder] UpdateDataMapByCompRoundId failed: err=[%v],param =  %v,%v,%v", err, compId, compEventType, gradeId)
			return
		}
	}
	return len(roundList) + 1, nil
}
func UpsertCompRoundExamRecord(ctx *gin.Context, tx *gorm.DB, param form.UpsertCompRoundRequest, compRoundId int) (err error) {
	compRoundExamModel := school.NewCompRoundExamModel(ctx)
	selectTiConf := data.SelectTiConf{}

	switch param.CompEventType {
	case components_const.CompEventTypeDaPing:
		fallthrough
	case components_const.CompEventTypeChineseDiathesis:
		fallthrough
	case components_const.CompEventTypeMathDiathesis:
		fallthrough
	case components_const.CompEventTypeEnglishDiathesis:
		{
			for _, v := range param.InteractiveDiathesisExamConfStruct {
				//tiScoreList, err := getTidListByExamId(ctx, strconv.FormatUint(v.ExamId, 10))
				//if err != nil {
				//	zlog.Warnf(ctx, "获取试卷 id = "+strconv.FormatUint(v.ExamId, 10))
				//	return errcode.SystemError("试卷 id = " + strconv.FormatUint(v.ExamId, 10) + "获取题目失败")
				//}
				//if len(tiScoreList) == 0 {
				//	return errcode.SystemError("试卷 id = " + strconv.FormatUint(v.ExamId, 10) + "题目数量为空, 没有确认试卷")
				//}
				endClassId := v.ClassIds[len(v.ClassIds)-1]
				examDraftId := fmt.Sprintf("InteractiveDiathesisExamDraft_%d_%d_%d_%d_%d_%d", param.CompId, param.CompEventType, param.GradeId, param.RoundNum, v.ExamId, endClassId)

				tiScoreList, err := NewCompService(ctx).getInteractiveExamDraftCache(examDraftId)
				if err != nil {
					zlog.Warnf(ctx, "获取试卷 id = "+strconv.FormatUint(v.ExamId, 10))
					return errcode.SystemError("试卷 id = " + strconv.FormatUint(v.ExamId, 10) + "获取题目失败")
				}
				if len(tiScoreList) == 0 {
					return errcode.SystemError("试卷 id = " + strconv.FormatUint(v.ExamId, 10) + "题目数量为空, 没有确认试卷")
				}
				groupType := GetGroupTypeByClassId(endClassId)
				if groupType == 0 {
					return errcode.SystemError(strconv.FormatUint(endClassId, 10) + "分类 id 不存在")
				}
				s := data.SelectTi{ClassId: endClassId, GroupType: groupType, Num: len(tiScoreList)}
				selectTiConf = append(selectTiConf, s)
			}
		}
	case components_const.CompEventTypeChineseTest:
		fallthrough
	case components_const.CompEventTypeMathTest:
		fallthrough
	case components_const.CompEventTypeEnglishTest:
		fallthrough
	case components_const.CompEventTypeMoralityTest:
		fallthrough
	case components_const.CompEventTypeScienceTest:
		fallthrough
	case components_const.CompEventTypeItTest:
		fallthrough
	case components_const.CompEventTypeLabourTest:
		fallthrough
	case components_const.CompEventTypeMusicTest:
		fallthrough
	case components_const.CompEventTypeArtTest:
		fallthrough
	case components_const.CompEventTypeSportsTest:
		fallthrough
	case components_const.CompEventTypeMentalHealthTest:
		fallthrough
	case components_const.CompEventTypeCommonTest:
		{
			var (
				classID   = param.TestExamConfStruct.ClassID
				groupType = param.TestExamConfStruct.GroupType
			)
			if groupType == 0 {
				return errcode.SystemError(strconv.FormatUint(classID, 10) + "分类 id 不存在")
			}
			for _, v := range param.TestExamConfStruct.SelectTiConf {
				s := data.SelectTi{ClassId: classID, GroupType: groupType, Category: v.CategoryID, Num: v.TiConf.Num}
				selectTiConf = append(selectTiConf, s)
			}
		}
	case components_const.CompEventTypeHuaRongRoad:
		{
			if param.ExamDraftId == "" {
				// 试卷没有变更
				return nil
			}
			for _, v := range param.HuaRongRoadExamConfStruct {
				endClassId := v.ClassIds[len(v.ClassIds)-1]
				groupType := GetGroupTypeByClassId(endClassId)
				if groupType == 0 {
					return errcode.SystemError(strconv.FormatUint(endClassId, 10) + "分类 id 不存在")
				}
				s := data.SelectTi{ClassId: endClassId, GroupType: groupType, Num: v.TiConf.Num}
				selectTiConf = append(selectTiConf, s)
			}
		}
	case components_const.CompEventTypeBatchAiZw:
		fallthrough
	case components_const.CompEventTypeAiEssay:
		{
			groupType := GetGroupTypeByClassId(components_const.AiEssayObjectType)
			if groupType == 0 {
				return errcode.SystemError(strconv.FormatUint(components_const.AiEssayObjectType, 10) + "分类 id 不存在")
			}
			s := data.SelectTi{ClassId: components_const.AiEssayObjectType, GroupType: groupType, Num: 1}
			selectTiConf = append(selectTiConf, s)
		}
	case components_const.CompEventTypeHanZiWrite:
		{
			groupType := GetGroupTypeByClassId(components_const.HanZiWriteObjectType)
			if groupType == 0 {
				return errcode.SystemError(strconv.FormatUint(components_const.HanZiWriteObjectType, 10) + "分类 id 不存在")
			}
			s := data.SelectTi{ClassId: components_const.HanZiWriteObjectType, GroupType: groupType, Num: len(param.HanZiWriteExamConfStruct.TiConf)}
			selectTiConf = append(selectTiConf, s)
		}
	case components_const.CompDpEventTypeHanZiWrite:
		{
			for _, v := range param.DpHanZiWriteExamConfStruct {
				groupType := GetGroupTypeByClassId(v.ClassId)
				if groupType == 0 {
					return errcode.SystemError(strconv.FormatUint(v.ClassId, 10) + "分类 id 不存在")
				}
				s := data.SelectTi{ClassId: v.ClassId, GroupType: groupType, Num: len(v.TiConf)}
				selectTiConf = append(selectTiConf, s)
			}
		}
	default:
		{
			if param.ExamDraftId == "" {
				// 试卷没有变更
				return nil
			}
			for _, v := range param.GeneralExamConfStruct {
				endClassId := v.ClassIds[len(v.ClassIds)-1]
				groupType := GetGroupTypeByClassId(endClassId)
				if groupType == 0 {
					return errcode.SystemError(strconv.FormatUint(endClassId, 10) + "分类 id 不存在")
				}
				s := data.SelectTi{ClassId: endClassId, GroupType: groupType, Num: v.TiConf.Num}
				selectTiConf = append(selectTiConf, s)
			}
		}
	}

	selectTiConfStr, err := json.Marshal(selectTiConf)
	if err != nil {
		return err
	}

	if param.CompRoundId != 0 {
		// _, err = compRoundExamModel.DeleteByCompRoundIds(param.CompId, []int{param.CompRoundId})
		// if err != nil {
		//	return err
		// }
		_, err = compRoundExamModel.UpdateDataByCompId(tx, param.CompRoundId, schoolDao.CompRoundExamDao{
			CompID:       param.CompId,
			CompRoundID:  compRoundId,
			ExamType:     param.ExamType,
			SelectTiConf: string(selectTiConfStr),
		})
		if err != nil {
			return err
		}

		compRoundId = param.CompRoundId
		return nil
	}
	err = compRoundExamModel.Add(tx, schoolDao.CompRoundExamDao{
		CompID:       param.CompId,
		CompRoundID:  compRoundId,
		ExamType:     param.ExamType,
		SelectTiConf: string(selectTiConfStr),
	})
	if err != nil {
		// 回滚
		return err
	}
	return nil
}

func UpsertCompRoundExamTiRecord(ctx *gin.Context, tx *gorm.DB, param form.UpsertCompRoundRequest, compRoundId int) (err error) {
	if param.ExamDraftId == "" &&
		(param.CompEventType != components_const.CompEventTypeDaPing &&
			param.CompEventType != components_const.CompEventTypeAiEssay &&
			param.CompEventType != components_const.CompEventTypeBatchAiZw &&
			param.CompEventType != components_const.CompEventTypeChineseDiathesis &&
			param.CompEventType != components_const.CompEventTypeMathDiathesis &&
			param.CompEventType != components_const.CompEventTypeChineseTest &&
			param.CompEventType != components_const.CompEventTypeMathTest &&
			param.CompEventType != components_const.CompEventTypeEnglishTest &&
			param.CompEventType != components_const.CompEventTypeMoralityTest &&
			param.CompEventType != components_const.CompEventTypeScienceTest &&
			param.CompEventType != components_const.CompEventTypeItTest &&
			param.CompEventType != components_const.CompEventTypeLabourTest &&
			param.CompEventType != components_const.CompEventTypeMusicTest &&
			param.CompEventType != components_const.CompEventTypeArtTest &&
			param.CompEventType != components_const.CompEventTypeSportsTest &&
			param.CompEventType != components_const.CompEventTypeMentalHealthTest &&
			param.CompEventType != components_const.CompEventTypeCommonTest &&
			param.CompEventType != components_const.CompEventTypeEnglishDiathesis &&
			param.CompEventType != components_const.CompEventTypeHanZiWrite &&
			param.CompEventType != components_const.CompDpEventTypeHanZiWrite) {

		// 非客观题需要草稿Id
		return nil
	}
	compRoundExamTiModel := school.NewCompRoundExamTiModel(ctx)
	if param.CompRoundId != 0 {
		_, err = compRoundExamTiModel.DeleteByCompId(tx, param.CompId, param.CompRoundId)
		if err != nil {
			return err
		}
		compRoundId = param.CompRoundId
	}
	var tiData []struct {
		TID       string
		ClassId   uint64
		GroupType uint64
		Score     int
		TimeLimit int
	}
	defer func() {
		if param.CompEventType == components_const.CompEventTypeDaPing ||
			param.CompEventType == components_const.CompEventTypeChineseDiathesis ||
			param.CompEventType == components_const.CompEventTypeMathDiathesis ||
			param.CompEventType == components_const.CompEventTypeChineseTest ||
			param.CompEventType == components_const.CompEventTypeMathTest ||
			param.CompEventType == components_const.CompEventTypeEnglishTest ||
			param.CompEventType == components_const.CompEventTypeMoralityTest ||
			param.CompEventType == components_const.CompEventTypeScienceTest ||
			param.CompEventType == components_const.CompEventTypeItTest ||
			param.CompEventType == components_const.CompEventTypeLabourTest ||
			param.CompEventType == components_const.CompEventTypeMusicTest ||
			param.CompEventType == components_const.CompEventTypeArtTest ||
			param.CompEventType == components_const.CompEventTypeSportsTest ||
			param.CompEventType == components_const.CompEventTypeMentalHealthTest ||
			param.CompEventType == components_const.CompEventTypeCommonTest ||
			param.CompEventType == components_const.CompEventTypeEnglishDiathesis {
			for _, v := range param.InteractiveDiathesisExamConfStruct {
				// 删除缓存
				tiKuClassId := v.ClassIds[len(v.ClassIds)-1]
				cacheKey1 := fmt.Sprintf("InteractiveDiathesisExamDraft_%d_%d_%d_%d_%d_%d", param.CompId, param.CompEventType, param.GradeId, param.RoundNum, v.ExamId, tiKuClassId)
				_, _ = helpers.RedisClient.Del(ctx, cacheKey1)
			}
		}
		if param.CompEventType == components_const.CompEventTypeAiEssay || param.CompEventType == components_const.CompEventTypeBatchAiZw {
			cacheKey2 := fmt.Sprintf("AiEssayExamDraft_%d_%d_%d_%d", param.CompId, param.CompEventType, param.GradeId, param.RoundNum)
			_, _ = helpers.RedisClient.Del(ctx, cacheKey2)
		}
	}()

	if param.CompEventType == components_const.CompEventTypeDaPing ||
		param.CompEventType == components_const.CompEventTypeChineseDiathesis ||
		param.CompEventType == components_const.CompEventTypeMathDiathesis ||
		param.CompEventType == components_const.CompEventTypeEnglishDiathesis {
		// 大屏没有草稿
		examIds := make(map[uint64]struct{}, 0)
		for _, v := range param.InteractiveDiathesisExamConfStruct {
			if _, ok := examIds[v.ExamId]; ok {
				zlog.Warnf(ctx, "试卷id重复"+strconv.FormatUint(v.ExamId, 10))
				return errcode.SystemError("试卷id重复" + strconv.FormatUint(v.ExamId, 10))
			}
			examIds[v.ExamId] = struct{}{}
			tiKuClassId := v.ClassIds[len(v.ClassIds)-1]
			examDraftId := fmt.Sprintf("InteractiveDiathesisExamDraft_%d_%d_%d_%d_%d_%d", param.CompId, param.CompEventType, param.GradeId, param.RoundNum, v.ExamId, tiKuClassId)

			tiScoreList, err := NewCompService(ctx).getInteractiveExamDraftCache(examDraftId)
			if err != nil {
				zlog.Warnf(ctx, "获取试卷 id = "+strconv.FormatUint(v.ExamId, 10))
				return errcode.SystemError("试卷 id = " + strconv.FormatUint(v.ExamId, 10) + "获取题目失败")
			}
			if len(tiScoreList) == 0 {
				return errcode.SystemError("试卷 id = " + strconv.FormatUint(v.ExamId, 10) + "题目数量为空")
			}
			zlog.Infof(ctx, "examId=%+v,tiScoreList=%+v", v.ExamId, tiScoreList) // 上线后可删除

			for _, info := range tiScoreList {
				score := info.Score
				if info.Score <= 0 {
					score = 5 // 如果没有获取到分数，默认5分
				}
				tiData = append(tiData, struct {
					TID       string
					ClassId   uint64
					GroupType uint64
					Score     int
					TimeLimit int
				}{
					info.Tid,
					tiKuClassId,
					v.GroupType,
					score * 100,
					int(v.TimeLimit) * 60,
				})
			}
		}
	} else if param.CompEventType == components_const.CompEventTypeChineseTest ||
		param.CompEventType == components_const.CompEventTypeMathTest ||
		param.CompEventType == components_const.CompEventTypeEnglishTest ||
		param.CompEventType == components_const.CompEventTypeMoralityTest ||
		param.CompEventType == components_const.CompEventTypeScienceTest ||
		param.CompEventType == components_const.CompEventTypeItTest ||
		param.CompEventType == components_const.CompEventTypeLabourTest ||
		param.CompEventType == components_const.CompEventTypeMusicTest ||
		param.CompEventType == components_const.CompEventTypeArtTest ||
		param.CompEventType == components_const.CompEventTypeSportsTest ||
		param.CompEventType == components_const.CompEventTypeMentalHealthTest ||
		param.CompEventType == components_const.CompEventTypeCommonTest {

		var (
			examID    = param.TestExamConfStruct.ExamID
			classID   = param.TestExamConfStruct.ClassID
			groupType = param.TestExamConfStruct.GroupType
		)
		examDraftId := fmt.Sprintf("TestExamDraft_%d_%d_%d_%d_%d_%d", param.CompId, param.CompEventType, param.GradeId, param.RoundNum, examID, classID)
		tiScoreList, err := NewCompService(ctx).getInteractiveExamDraftCache(examDraftId)
		if err != nil {
			zlog.Warnf(ctx, "获取试卷 id = "+strconv.FormatUint(examID, 10))
			return errcode.SystemError("试卷 id = " + strconv.FormatUint(examID, 10) + "获取题目失败")
		}
		if len(tiScoreList) == 0 {
			return errcode.SystemError("试卷 id = " + strconv.FormatUint(examID, 10) + "题目数量为空")
		}
		zlog.Infof(ctx, "examId=%+v,tiScoreList=%+v", examID, tiScoreList) // 上线后可删除

		for _, info := range tiScoreList {
			score := info.Score
			if info.Score <= 0 {
				score = 5 // 如果没有获取到分数，默认5分
			}
			tiData = append(tiData, struct {
				TID       string
				ClassId   uint64
				GroupType uint64
				Score     int
				TimeLimit int
			}{
				info.Tid,
				classID,
				groupType,
				score * 100,
				0,
			})
		}
	} else if param.CompEventType == components_const.CompEventTypeAiEssay || param.CompEventType == components_const.CompEventTypeBatchAiZw {
		examDraftId := fmt.Sprintf("AiEssayExamDraft_%d_%d_%d_%d", param.CompId, param.CompEventType, param.GradeId, param.RoundNum)
		tiList, err := NewCompService(ctx).getAiEssayExamDraftCache(examDraftId)
		if err != nil {
			zlog.Warnf(ctx, "获取题目列表失败 = %s", examDraftId)
			return errcode.SystemError("获取题目列表失败 = " + examDraftId)
		}
		groupType := GetGroupTypeByClassId(components_const.AiEssayObjectType)
		if groupType == 0 {
			return errcode.SystemError(strconv.FormatUint(components_const.AiEssayObjectType, 10) + "分类 id 不存在")
		}
		for _, v := range tiList {
			tiData = append(tiData, struct {
				TID       string
				ClassId   uint64
				GroupType uint64
				Score     int
				TimeLimit int
			}{
				v,
				components_const.AiEssayObjectType,
				groupType,
				0,
				0,
			})
		}
	} else if param.CompEventType == components_const.CompEventTypeHanZiWrite {
		for _, v := range param.HanZiWriteExamConfStruct.TiConf {
			tiData = append(tiData, struct {
				TID       string
				ClassId   uint64
				GroupType uint64
				Score     int
				TimeLimit int
			}{
				v.TId,
				components_const.HanZiWriteObjectType,
				components_const.HanZiWriteObjectType,
				v.Score * 100,
				param.HanZiWriteExamConfStruct.TimeLimit * 60,
			})
		}
	} else if param.CompEventType == components_const.CompDpEventTypeHanZiWrite {
		for _, v := range param.DpHanZiWriteExamConfStruct {
			groupType := GetGroupTypeByClassId(v.ClassId)
			if groupType == 0 {
				return errcode.SystemError(strconv.FormatUint(v.ClassId, 10) + "分类 id 不存在")
			}
			for _, vv := range v.TiConf {
				tiData = append(tiData, struct {
					TID       string
					ClassId   uint64
					GroupType uint64
					Score     int
					TimeLimit int
				}{
					vv.TId,
					v.ClassId,
					groupType,
					vv.Score * 100,
					v.TimeLimit * 60,
				})
			}
		}
	} else {
		objExamDraft, err := NewCompService(ctx).getExamDraftCache(ctx, param.ExamDraftId)
		if err != nil {
			return err
		}
		classScoreMap := make(map[uint64]int)
		classTotalMap := make(map[uint64]int)
		if param.CompEventType == components_const.CompEventTypeHuaRongRoad {
			for _, v := range param.HuaRongRoadExamConfStruct {
				classScoreMap[v.ClassIds[len(v.ClassIds)-1]] = v.TiConf.Score * 100
				classTotalMap[v.ClassIds[len(v.ClassIds)-1]] = v.TiConf.Total
			}
		} else {
			for _, v := range param.GeneralExamConfStruct {
				classScoreMap[v.ClassIds[len(v.ClassIds)-1]] = v.TiConf.Score * 100
				classTotalMap[v.ClassIds[len(v.ClassIds)-1]] = v.TiConf.Total
			}
		}
		for _, group := range objExamDraft.TiGroup {
			if len(group.ClassIds) == 0 {
				s, _ := json.Marshal(objExamDraft)
				zlog.Warnf(ctx, "[UpsertCompRoundExamTiRecord] class id 为空:"+string(s))
				return errcode.SystemError("草稿分类 id 为空")
			}
			if group.GroupType == 0 {
				s, _ := json.Marshal(objExamDraft)
				zlog.Warnf(ctx, "[UpsertCompRoundExamTiRecord] class id 为空:"+string(s))
				return errcode.SystemError("草稿分组为空")
			}
			tiKuClassId := group.ClassIds[len(group.ClassIds)-1]
			total, ok := classTotalMap[tiKuClassId]
			if !ok {
				zlog.Warnf(ctx, "[UpsertCompRoundExamTiRecord] 题分类不存在:%+v", param)
				return errcode.SystemError("题分类不存在")
			}
			if total != len(group.TidList) {
				zlog.Warnf(ctx, "[UpsertCompRoundExamTiRecord] 题总数不符合:%+v:草稿:%+v", param, objExamDraft)
				return errcode.SystemError("题总数不符合")
			}
			for _, tid := range group.TidList {
				tiData = append(tiData, struct {
					TID       string
					ClassId   uint64
					GroupType uint64
					Score     int
					TimeLimit int
				}{
					tid,
					tiKuClassId,
					group.GroupType,
					classScoreMap[tiKuClassId],
					0,
				})
			}
		}
	}

	if len(tiData) == 0 {
		zlog.Warnf(ctx, "[UpsertCompRoundExamTiRecord] 题目列表为空:%+v", param)
		return errcode.SystemError("题目列表为空")
	}

	compStuRoundList := make([]schoolDao.CompRoundExamTiDao, 0, len(tiData))
	for k, v := range tiData {
		compStuRoundList = append(compStuRoundList, schoolDao.CompRoundExamTiDao{
			CompID:      param.CompId,
			CompRoundID: compRoundId,
			//TiKuClassID: v.ClassId,
			GroupType:  v.GroupType,
			TID:        v.TID,
			SortNum:    k + 1,
			TimeLimit:  v.TimeLimit,
			Score:      v.Score,
			CreateTime: int(time.Now().Unix()),
			UpdateTime: int(time.Now().Unix()),
		})
	}
	err = compRoundExamTiModel.BatchAdd(tx, compStuRoundList)
	if err != nil {
		zlog.Warnf(ctx, "compRoundExamTiModel.BatchAdd error:%+v", err)
		return err
	}
	return

}
