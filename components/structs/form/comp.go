package form

import (
	tikuCli "lxjxmis-go/api/tiku"
	"lxjxmis-go/components/structs/data"
)

type GetCompRoundIdRequest struct {
	CompID        int `json:"compId" form:"compId" binding:"required"`
	CompEventType int `json:"compEventType" form:"compEventType" binding:"required"`
	GradeID       int `json:"gradeId" form:"gradeId" binding:"required"`
}

type GetCompAndRoundIdRequest struct {
	CompID        int `json:"compId" form:"compId" binding:"required"`
	CompEventType int `json:"compEventType" form:"compEventType" binding:"required"`
	GradeID       int `json:"gradeId" form:"gradeId" binding:"required"`
	CompRoundID   int `json:"compRoundId" form:"compRoundId"`
}
type CompEventListRequest struct {
	CompID        int `json:"compId" form:"compId" binding:"required"`
	CompEventType int `json:"compEventType" form:"compEventType" binding:"required"`
	GradeID       int `json:"gradeId" form:"gradeId" binding:"required"`
}

type ShuDuCongMingGeAddTiRequest struct {
	Content       string `json:"content" form:"content"`
	ClassIdLevel1 string `json:"classIdLevel1" form:"classIdLevel1"`
	ClassIdLevel2 string `json:"classIdLevel2" form:"classIdLevel2"`
	//ClassType     int    `json:"classType" form:"classType"` // 分类
}

type CompRoundInfoRequest struct {
	CompRoundID int `json:"compRoundId" form:"compRoundId" binding:"required"`
}

type GetTiInfoListRequest struct {
	CompID      int      `json:"compId" form:"compId" binding:"required"`
	CompRoundID int      `json:"compRoundId" form:"compRoundId" binding:"required"`
	TIDs        []string `json:"tIds" form:"tIds" binding:"required"`
}

type RosterListRequest struct {
	CompID   int    `json:"compId" form:"compId" binding:"required"`
	GradeID  int    `json:"gradeId" form:"gradeId" binding:"required"`
	ClassID  string `json:"classId" form:"classId" binding:"required"`
	SchoolID int    `json:"schoolId" form:"schoolId" binding:"required"`
}

type GradeListRequest struct {
	CompID        int `json:"compId" form:"compId" binding:"required"`
	SchoolID      int `json:"schoolId" form:"schoolId" binding:"required"`
	CompEventType int `json:"compEventType" form:"compEventType"`
	// CompEventType int `json:"compEventType" form:"compEventType" binding:"required"`
}

type AreaListRequest struct {
	CompID int `json:"compId" form:"compId" binding:"required"`
}

type ClassListRequest struct {
	CompID   int `json:"compId" form:"compId" binding:"required"`
	SchoolID int `json:"schoolId" form:"schoolId" binding:"required"`
	GradeID  int `json:"gradeId" form:"gradeId" binding:"required"`
}

type CompBaseInfoRequest struct {
	CompID int `json:"compId" form:"compId" binding:"required"`
}

type SearchByNameRequest struct {
	Name    string `json:"name" form:"name"`
	ActType int    `json:"actType" form:"actType" binding:"required"`
}

type ListRequest struct {
	Name         string `json:"name" form:"name"`
	Status       int    `json:"status" form:"status"`
	ActType      int    `json:"actType" form:"actType" binding:"required"`
	CompId       int    `json:"compId" form:"compId"`
	OperatorName string `json:"operatorName" form:"operatorName"`
	Rn           int    `json:"rn" form:"rn"`
	Pn           int    `json:"pn" form:"pn"`
}

type UpdateStatusRequest struct {
	CompID int `json:"compId" form:"compId" binding:"required"`
	Status int `json:"status" form:"status" binding:"required"`
}

type UpsertCompBaseRequest struct {
	CompID          int    `json:"compId" form:"compId"`
	ActType         int    `json:"actType" form:"actType" binding:"required"`
	Name            string `json:"name" form:"name"  binding:"required"`
	StartTime       int    `json:"startTime" form:"startTime" binding:"required"`
	CompEventList   string `json:"compEventList" form:"compEventList" binding:"required"`
	EndTime         int    `json:"endTime" form:"endTime" binding:"required"`
	AreaList        string `json:"areaList" form:"areaList" binding:"required"`
	SchoolList      string `json:"schoolList" form:"schoolList" binding:"required"`
	StuType         int    `json:"stuType" form:"stuType"`
	Subject         int    `json:"subject" form:"subject"`
	LargeScreen     int    `json:"largeScreen" form:"largeScreen"`
	RosterURL       string `json:"rosterUrl" form:"rosterUrl"`
	RosterName      string `json:"rosterName" form:"rosterName"`
	BackgroundPic   string `json:"backgroundPic" form:"backgroundPic"`
	TestMode        int    `json:"testMode" form:"testMode"`
	SupportReport   int    `json:"supportReport" form:"supportReport"`
	SignUpStartTime int    `json:"signUpStartTime" form:"signUpStartTime"`
	SignUpEndTime   int    `json:"signUpEndTime" form:"signUpEndTime"`
	AiRegistNotice  string `json:"aiRegistNotice" form:"aiRegistNotice"`
	AiUploadNotice  string `json:"aiUploadNotice" form:"aiUploadNotice"`
}

type InviteCodeRequest struct {
	InviteCode string `json:"inviteCode" form:"inviteCode" binding:"required"`
	CompId     int    `json:"compId" form:"compId" binding:"required"`
}

type SchoolChoseRequest struct {
	CountyId   int    `json:"countyId" form:"countyId"`
	CompId     int    `json:"compId" form:"compId" binding:"required"`
	Pn         int    `json:"pn" form:"pn"`
	Rn         int    `json:"rn" form:"rn"`
	SchoolName string `json:"schoolName" form:"schoolName"`
}

type HmcChoseRequest struct {
	CompId      int `json:"compId" form:"compId" binding:"required"`
	StuRosterId int `json:"stuRosterId" form:"stuRosterId" binding:"required"`
}

type UserInfoLZReq struct {
	CountyId      int    `json:"countyId" form:"countyId" binding:"required"`
	CompId        int    `json:"compId" form:"compId" binding:"required"`
	Token         string `json:"token" form:"token" binding:"required"`
	ProvinceId    int    `json:"provinceId" form:"provinceId" binding:"required"`
	CityId        int    `json:"cityId" form:"cityId" binding:"required"`
	SchoolId      int    `json:"schoolId" form:"schoolId" binding:"required"`
	GradeId       int    `json:"gradeId" form:"gradeId" binding:"required"`
	ClassId       string `json:"classId" form:"classId" binding:"required"`
	StuName       string `json:"stuName" form:"stuName" binding:"required"`
	CompRoundId   int64  `json:"compRoundId" form:"compRoundId"`
	CompEventType int    `json:"compEventType" form:"compEventType"`
}

type CompDetailRequest struct {
	CompId int `json:"compId" form:"compId" binding:"required"`
}

type UpsertCompRoundRequest struct {
	CompId                             int                               `json:"compId" form:"compId" binding:"required"`
	CompRoundId                        int                               `json:"compRoundId" form:"compRoundId"`
	Name                               string                            `json:"name" form:"name"`
	StartTime                          int                               `json:"startTime" form:"startTime"`
	EndTime                            int                               `json:"endTime" form:"endTime"`
	CompEventType                      int                               `json:"compEventType" form:"compEventType" binding:"required"`
	GradeId                            int                               `json:"gradeId" form:"gradeId" binding:"required"`
	RoundNum                           int                               `json:"roundNum" form:"roundNum"`
	ShowRank                           int                               `json:"showRank" form:"showRank"`
	ExamType                           int                               `json:"examType" form:"examType"`
	CompTimes                          int                               `json:"compTimes" form:"compTimes"`
	ExamConf                           string                            `json:"examConf" form:"examConf"`
	TimeLimit                          int                               `json:"timeLimit" form:"timeLimit"`
	AnswerSet                          int                               `json:"answerSet" form:"answerSet"`
	ExamDraftId                        string                            `json:"examDraftId" form:"examDraftId"`
	ArticleType                        int                               `json:"articleType" form:"articleType"`         // AI 征文文章类型
	ArticleTheme                       int                               `json:"articleTheme" form:"articleTheme"`       // AI 征文文章题材
	ArticleWordsNum                    int                               `json:"articleWordsNum" form:"articleWordsNum"` // 文章字数
	Content                            string                            `json:"content" form:"content"`                 // 文章内容
	Other                              string                            `json:"other" form:"other"`                     // 其它
	QuestionShow                       int                               `json:"questionShow" form:"questionShow"`       // 题目展示，1通用，2定制
	CustomMade                         string                            `json:"customMade" form:"customMade"`           // 题目展示定制内容
	ResultShow                         int                               `json:"resultShow" form:"resultShow"`           // 结果展示，1仅展示得分，2仅展示等级，3得分+等级
	CommentShow                        int                               `json:"commentShow" form:"commentShow"`         // 评语展示，1展示缺点，2不展示缺点建议
	ScoringRule                        string                            `json:"scoringRule" form:"scoringRule"`         // 打分规则
	HzScoringRule                      int                               `json:"hzScoringRule" form:"hzScoringRule"`     // 汉字听写打分规则
	GeneralExamConfStruct              data.GeneralExamConf              `json:"-"`
	HuaRongRoadExamConfStruct          data.HuaRongRoadExamConf          `json:"-"`
	InteractiveDiathesisExamConfStruct data.InteractiveDiathesisExamConf `json:"-"`
	HanZiWriteExamConfStruct           data.HanZiWriteExamConf           `json:"-"`
	DpHanZiWriteExamConfStruct         []data.HanZiWriteExamConf         `json:"-"`
	TestExamConfStruct                 data.TestExamConf                 `json:"-"`
	//DiathesisExamConfStruct            data.DiathesisExamConf            `json:"-"`
}

type DelCompRoundRequest struct {
	CompId      int `json:"compId" form:"compId" binding:"required"`
	CompRoundId int `json:"compRoundId" form:"compRoundId"`
}

type GetRoundNumListRequest struct {
	CompID        int `json:"compId" form:"compId" binding:"required" binding:"required"`
	CompEventType int `json:"compEventType" form:"compEventType" binding:"required"`
}

// 试卷&试卷草稿
type GetExamInfoRequest struct {
	CompID      int `json:"compId" form:"compId" binding:"required"`
	CompRoundID int `json:"compRoundId" form:"compRoundId" binding:"required"`
	StuUid      int `json:"stuUid" form:"stuUid" binding:"required"`
	//SubExamId   int `json:"subExamId" form:"subExamId"`
}

// 试卷详情
type GetExamDetailRequest struct {
	CompID      int `json:"compId" form:"compId" binding:"required"`
	CompRoundID int `json:"compRoundId" form:"compRoundId" binding:"required"`
	StuUid      int `json:"stuUid" form:"stuUid" binding:"required"`
	SubExamId   int `json:"subExamId" form:"subExamId" binding:"required"`
}

// ExamDraftPreviewRequest 预览试卷草稿req
// ExamConf的例子: [{"classIds":[1,2],"info":{"num":3,"total":50,"score":5}}]
type ExamDraftPreviewRequest struct {
	CompId              int                      `json:"compId" form:"compId" binding:"required"`               // 比赛ID，必填
	CompEventType       int                      `json:"compEventType" form:"compEventType" binding:"required"` // 赛项类型，必填，1-数独，2-24点
	GradeId             int                      `json:"gradeId" form:"gradeId" binding:"required"`             // 年级ID，必填
	ExamDraftId         string                   `json:"examDraftId" form:"examDraftId"`                        // 要预览的试卷草稿ID，字符串类型，可选。为空且compId为0时，则表明要新建一个试卷草稿
	CompRoundId         int                      `json:"compRoundId" form:"compRoundId"`                        // 轮次ID，可选
	RoundNum            int                      `json:"roundNum" form:"roundNum" binding:"required"`           // 轮次序号，可选，当前是第几轮，默认是1
	ExamConfStr         string                   `json:"examConf" form:"examConf"`                              // 试卷抽题的配置的入参结构，字符串类型，可选。能够解析为json格式，classIds是题库分类的路径
	Pn                  int                      `json:"pn" form:"pn"`                                          // 页码,从0开始
	Rn                  int                      `json:"rn" form:"rn"`                                          // 每页数量,最大40,默认10
	IsEdit              int                      `json:"isEdit" form:"isEdit"`                                  // 是否是编辑状态，默认0
	GeneralExamConf     data.GeneralExamConf     `json:"-"`                                                     // 试卷抽题的配置的内存结构，由 ExamConfStr 解json生成
	HuaRongRoadExamConf data.HuaRongRoadExamConf `json:"-"`                                                     // 试卷抽题的配置的内存结构，由 ExamConfStr 解json生成
}

// ExamDraftPreviewResponse 预览试卷草稿resp
type ExamDraftPreviewResponse struct {
	ExamDraftId string                           `json:"examDraftId"` // 本次预览的试卷草稿ID
	Total       int                              `json:"total"`       // 题目总数
	Pn          int                              `json:"pn"`          // 页码,从0开始
	Rn          int                              `json:"rn"`          // 每页数量,最大40,默认10
	List        []ExamDraftPreviewResponseTiInfo `json:"list"`        // 题目信息
}

type ExamDraftPreviewResponseTiInfo struct {
	tikuCli.TiInfoUI
	ClassLabel []tikuCli.Label `json:"classLabel"` // 题库分类标签
}

type UpsertAllCompRoundRequest struct {
	CompId        int    `json:"compId" form:"compId" binding:"required"`
	CompRoundList string `json:"compRoundList" form:"compRoundList" binding:"required"`
}

type AchivementReq struct {
	CompId        int    `json:"compId" form:"compId" binding:"required"`
	CompRoundId   int    `json:"compRoundId" form:"compRoundId"`
	Pn            int    `json:"pn" form:"pn"`
	Rn            int    `json:"rn" form:"rn" binding:"required"`
	StuUid        int    `json:"stuUid" form:"stuUid"`
	CompEventType int    `json:"compEventType" form:"compEventType"`
	SchoolId      int    `json:"schoolId" form:"schoolId"`
	Export        int    `json:"export" form:"export"`
	StuName       string `json:"stuName" form:"stuName"`
	Status        int    `json:"status" form:"status"`
	StuNo         int64  `json:"stuNo" form:"stuNo"`
}

type GetAnswerParamReq struct {
	CompId        int `json:"compId" form:"compId" binding:"required"`
	CompRoundId   int `json:"compRoundId" form:"compRoundId"`
	Pn            int `json:"pn" form:"pn" `
	Rn            int `json:"rn" form:"rn" binding:"required"`
	StuUid        int `json:"stuUid" form:"stuUid"`
	Status        int `json:"status" form:"status"`
	CompEventType int `json:"compEventType" form:"compEventType"`
}

type GetUserScoreRankListReq struct {
	CompId        int    `json:"compId" form:"compId" binding:"required"`
	CompRoundIds  string `json:"compRoundIds" form:"compRoundIds"`
	CompEventType int    `json:"compEventType" form:"compEventType"`
}

type GetAnswerDetailReq struct {
	CompId        int    `json:"compId" form:"compId" binding:"required"`
	CompRoundId   int    `json:"compRoundId" form:"compRoundId" binding:"required"`
	CompEventType int    `json:"compEventType" form:"compEventType" binding:"required"`
	AnswerId      int    `json:"answerId" form:"answerId" binding:"required"`
	StuUid        int    `json:"stuUid" form:"stuUid"  binding:"required"`
	TiId          string `json:"tiId" form:"tiId" `
	SubExamId     int    `json:"subExamId" form:"subExamId"`
}

type SearchNameReq struct {
	Name          string `json:"name" form:"name"`
	CompId        int    `json:"compId" form:"compId"`
	CompRoundId   int    `json:"compRoundId" form:"compRoundId"`
	CompEventType int    `json:"compEventType" form:"compEventType"`
}

type DownloadRosterFileRequest struct {
	Pid        string `json:"pid" form:"pid" binding:"required"`
	RosterName string `json:"rosterName" form:"rosterName" binding:"required"`
}

type ConfirmExamRequest struct {
	CompId        int `json:"compId" form:"compId" binding:"required"`               // 比赛ID，必填
	CompEventType int `json:"compEventType" form:"compEventType" binding:"required"` // 赛项类型，必填，1-数独，2-24点
	GradeId       int `json:"gradeId" form:"gradeId" binding:"required"`             // 年级ID，必填
	ExamId        int `json:"examId" form:"examId" binding:"required"`               // 试卷ID，必填
}

type ExamPreviewRequest struct {
	ExamId int `json:"examId" form:"examId" binding:"required"` // 试卷ID，必填
	CompId int `json:"compId" form:"compId" binding:"required"` // 比赛ID，必填
}

type DpTchStuParam struct {
	CompId      int    `json:"compId" form:"compId" binding:"required"`
	CompRoundId int    `json:"compRoundId" form:"compRoundId"`
	SchoolId    int    `json:"schoolId" form:"schoolId"`
	GradeId     int    `json:"gradeId" form:"gradeId"`
	ClassId     string `json:"classId" form:"classId"`
}

type StuAnswerParam struct {
	CountyId      int    `json:"countyId" form:"countyId"`
	CompId        int64  `json:"compId" form:"compId" binding:"required"`
	ProvinceId    int    `json:"provinceId" form:"provinceId" binding:"required"`
	CityId        int    `json:"cityId" form:"cityId" binding:"required"`
	SchoolId      int    `json:"schoolId" form:"schoolId" binding:"required"`
	GradeId       int    `json:"gradeId" form:"gradeId" binding:"required"`
	ClassId       string `json:"classId" form:"classId" binding:"required"`
	StuName       string `json:"stuName" form:"stuName" binding:"required"`
	StuUid        int64  `json:"stuUid" form:"stuUid" binding:"required"`
	CompRoundId   int64  `json:"compRoundId" form:"compRoundId" binding:"required"`
	CompEventType int    `json:"compEventType" form:"compEventType"`
	BestTime      int64  `json:"bestTime" form:"bestTime"`
	AnswerTimes   int64  `json:"answerTimes" form:"answerTimes"`
	HighScore     int    `json:"highScore" form:"highScore"`
	HighTime      int    `json:"highTime" form:"highTime"`
	CompAnswerId  int64  `json:"compAnswerId" form:"compAnswerId"`
	Ext           string `json:"ext" form:"ext"`
}

type DelDpScoreDataParam struct {
	CompId        int64  `json:"compId" form:"compId" binding:"required"`
	CompRoundId   int64  `json:"compRoundId" form:"compRoundId"`
	SchoolId      int    `json:"schoolId" form:"schoolId"`
	GradeId       int    `json:"gradeId" form:"gradeId"`
	ClassId       string `json:"classId" form:"classId"`
	CompEventType int    `json:"compEventType" form:"compEventType" binding:"required"`
}

type GetStuListByUidsRequest struct {
	CompId      int    `json:"compId" form:"compId" binding:"required"`
	CompRoundId int    `json:"compRoundId" form:"compRoundId"`
	SchoolId    int    `json:"schoolId" form:"schoolId"`
	GradeId     int    `json:"gradeId" form:"gradeId"`
	ClassId     string `json:"classId" form:"classId"`
	Uids        []int  `json:"uids" form:"uids"`
}

type UpdateAiDataStatus struct {
	CompId      int64 `json:"compId" form:"compId" binding:"required"`
	CompRoundId int64 `json:"compRoundId" form:"compRoundId" binding:"required"`
	StuUid      int64 `json:"stuUid" form:"stuUid"  binding:"required"`
	AnswerId    int64 `json:"answerId" form:"answerId"  binding:"required"`
}

type ShiShiDpUidParam struct {
	CompId      int `json:"compId" form:"compId" binding:"required"`
	CompRoundId int `json:"compRoundId" form:"compRoundId" binding:"required"`
	SchoolId    int `json:"schoolId" form:"schoolId" binding:"required"`
	GradeId     int `json:"gradeId" form:"gradeId" binding:"required"`
}

type ReportListRequest struct {
	CompID        int `json:"compId" form:"compId" binding:"required"`
	CompEventType int `json:"compEventType" form:"compEventType" binding:"required"`
}

type ReportGradeListRequest struct {
	CompID        int `json:"compId" form:"compId" binding:"required"`
	CompEventType int `json:"compEventType" form:"compEventType" binding:"required"`
	SchoolID      int `json:"schoolId" form:"schoolId" binding:"required"`
}
type ReportCreateRequest struct {
	CompID        int    `json:"compId" form:"compId" binding:"required"`
	CompEventType int    `json:"compEventType" form:"compEventType" binding:"required"`
	Title         string `json:"title" form:"title" binding:"required"`
	ReportType    int    `json:"reportType" form:"reportType" binding:"required"`
	RegionIds     string `json:"regionIds" form:"regionIds" binding:"required"`
	Description   string `json:"description" form:"description" binding:"required"`
}

type ReportIdRequest struct {
	ReportId int `json:"reportId" form:"reportId" binding:"required"`
}

type ReportUpdateStatusRequest struct {
	ReportId int `json:"reportId" form:"reportId" binding:"required"`
	Status   int `json:"status" form:"status" binding:"required"`
}

type BatchAiZwReq struct {
	CompId        int    `json:"compId" form:"compId" binding:"required"`
	CompRoundId   int    `json:"compRoundId" form:"compRoundId"`
	Pn            int    `json:"pn" form:"pn"`
	Rn            int    `json:"rn" form:"rn" binding:"required"`
	StuUid        int    `json:"stuUid" form:"stuUid"`
	CompEventType int    `json:"compEventType" form:"compEventType"`
	SchoolId      int    `json:"schoolId" form:"schoolId"`
	Export        int    `json:"export" form:"export"`
	StuName       string `json:"stuName" form:"stuName"`
	MatchStatus   int    `json:"matchStatus" form:"matchStatus"`
	StuNo         string `json:"stuNo" form:"stuNo"`
}

type AiUserInfoLZReq struct {
	CountyId      int    `json:"countyId" form:"countyId" binding:"required"`
	CompId        int    `json:"compId" form:"compId" binding:"required"`
	ProvinceId    int    `json:"provinceId" form:"provinceId" binding:"required"`
	CityId        int    `json:"cityId" form:"cityId" binding:"required"`
	SchoolId      int    `json:"schoolId" form:"schoolId" binding:"required"`
	GradeId       int    `json:"gradeId" form:"gradeId" binding:"required"`
	ClassId       string `json:"classId" form:"classId" binding:"required"`
	StuName       string `json:"stuName" form:"stuName" binding:"required"`
	CompRoundId   int64  `json:"compRoundId" form:"compRoundId"`
	CompEventType int    `json:"compEventType" form:"compEventType"`
	Token         string `json:"token" form:"token"`
}
