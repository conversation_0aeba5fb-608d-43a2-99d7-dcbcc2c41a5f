package data

// comp_event_list:[{"compEventType":"赛项枚举1","gradeIds":[3,4,5]}]
type CompEventList []struct {
	CompEventType int    `json:"compEventType"`
	GradeIds      []int  `json:"gradeIds"`
	CompEventFrom int    `json:"compEventFrom"`
	Rules         string `json:"rules"`
}

//	GeneralExamConf[]
//	@Description: 试卷配置
//
// exam_conf:[{"classIds":[1,2],"tiConf":{"num":3,"total":5,"score":5}}]
type GeneralExamConf []struct {
	ClassIds  []uint64 `json:"classIds"`
	GroupType uint64   `json:"groupType"`
	TiConf    struct {
		Num   int `json:"num"`   // 抽取数
		Total int `json:"total"` // 总数
		Score int `json:"score"` // 分值
	} `json:"tiConf"`
}

type TestExamConf struct {
	ExamID       uint64 `json:"examId"`
	ClassID      uint64 `json:"classId"`
	GroupType    uint64 `json:"groupType"`
	SelectTiConf []struct {
		CategoryID uint64 `json:"categoryId"`
		TiConf     struct {
			Num   int `json:"num"`   // 抽取数
			Total int `json:"total"` // 总数
			Score int `json:"score"` // 分值
		} `json:"tiConf"`
	} `json:"selectTiConf"`
}

// exam_conf:[{"classIds":[1,2],"tiConf":{"num":3,"total":5,"score":5}}]
type HuaRongRoadExamConf []struct {
	ClassIds  []uint64 `json:"ClassIds"`
	ClassConf []uint64 `json:"classConf"`
	GroupType uint64   `json:"groupType"`
	TiConf    struct {
		Num   int `json:"num"`   // 抽取数
		Total int `json:"total"` // 总数
		Score int `json:"score"` // 分值
	} `json:"tiConf"`
}

// InteractiveDiathesisExamConf[]
// @Description: 大屏比赛试卷配置
type InteractiveDiathesisExamConfValue struct {
	ClassIds  []uint64 `json:"classIds"` // 分类id
	ExamId    uint64   `json:"examId"`   // 试卷id
	GroupType uint64   `json:"groupType"`
	TimeLimit uint64   `json:"timeLimit"` // 每题时长
}
type InteractiveDiathesisExamConf []InteractiveDiathesisExamConfValue

// 测评试卷结构
type HanZiWriteExamConf struct {
	ExamId    int              `json:"examId"`
	ClassId   uint64           `json:"classId"`   // 分类id
	TimeLimit int              `json:"timeLimit"` // 每题时长
	TiConf    HanZiWriteTiConf `json:"tiConf"`
}
type HanZiWriteTiConf []struct {
	TId   string `json:"tId"`
	Score int    `json:"score"`
}

// 通用比赛素质题试卷配置
type DiathesisExamConf struct {
	ClassIds  []uint64 `json:"classIds"` // 分类id
	ExamId    uint64   `json:"examId"`   // 试卷id
	GroupType uint64   `json:"groupType"`
}

// select_ti_conf:[{"classId":1,"num":1}]
type SelectTiConf []SelectTi

type SelectTi struct {
	ClassId   uint64 `json:"classId"`
	GroupType uint64 `json:"groupType"`
	Category  uint64 `json:"category"`
	Num       int    `json:"num"`
}

// AI 定制化图片 {"backgroundPic": "","contentPic": [""]}
type AiCustomMade struct {
	BackgroundPic string   `json:"backgroundPic"`
	ContentPic    []string `json:"contentPic"`
}

// 打分规则  [{"scoringRule":1,"weight":1}]
type ScoringRule struct {
	ScoringRule int `json:"scoringRule"`
	Weight      int `json:"weight"`
}
type AiScoringRuleList []ScoringRule
type AreaListToC struct {
	ProvinceList map[int]string `json:"provinceList"`
	CityList     map[int]string `json:"cityList"`
	CountyList   map[int]string `json:"countyList"`
}

type RosterRowData struct {
	CompID int `json:"compId"` // 比赛id
	// StuUID     int    `json:"stuUid"`     // 学生uid
	StuName    string `json:"stuName"`    // 学生姓名
	ProvinceID int    `json:"provinceId"` // 省id
	CityID     int    `json:"cityId"`     // 市id
	CountyID   int    `json:"countyId"`   // 区id
	SchoolID   int    `json:"schoolId"`   // 学校id
	GradeID    int8   `json:"gradeId"`    // 年级id
	ClassID    string `json:"classId"`    // 班级id
}

type TiFields struct {
	Tid     string `json:"tid"`
	SortNum int    `json:"sortNum"`
}

type TiGroup struct {
	GroupName string   `json:"groupName"`
	TidList   []string `json:"tidList"`
	GroupType uint64   `json:"groupType"`
	GroupSeq  uint64   `json:"groupSeq"`
}
